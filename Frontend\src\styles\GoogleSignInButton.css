/* Google Sign-In <PERSON><PERSON> Styles */
.google-signin-btn {
  width: 100%;
  padding: 14px var(--basefont);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.google-signin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.google-signin-btn__content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.google-signin-btn__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #4285f4; /* Google Blue as fallback */
}

.google-signin-btn__text {
  font-weight: 500;
  color: var(--text-color);
}

/* Primary variant - Google brand colors */
.google-signin-btn--primary {
  background: linear-gradient(
    135deg,
    #4285f4 0%,
    #34a853 25%,
    #fbbc05 50%,
    #ea4335 75%
  );
  color: var(--white);
  border: none;
}

.google-signin-btn--primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

.google-signin-btn--primary:active:not(:disabled) {
  transform: translateY(0);
}

/* Secondary variant - White background matching Figma design */
.google-signin-btn--secondary {
  background-color: var(--white);
  color: var(--text-color);
  border: 1px solid var(--light-gray);
}

.google-signin-btn--secondary:hover:not(:disabled) {
  background-color: var(--bg-gray);
  border-color: var(--dark-gray);
  box-shadow: var(--box-shadow-light);
}

/* Google icon with authentic colors for secondary variant */
.google-signin-btn--secondary .google-signin-btn__icon {
  background: linear-gradient(
    135deg,
    #4285f4 0%,
    #34a853 25%,
    #fbbc05 50%,
    #ea4335 75%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* Fallback for browsers that don't support background-clip */
  color: #4285f4;
}

/* Outline variant */
.google-signin-btn--outline {
  background-color: transparent;
  color: #3c4043;
  border: 2px solid #dadce0;
}

.google-signin-btn--outline:hover:not(:disabled) {
  background-color: #f8f9fa;
  border-color: #4285f4;
}

.google-signin-btn--outline .google-signin-btn__icon {
  color: #4285f4;
}

/* Loading state */
.google-signin-btn:disabled .google-signin-btn__icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Focus styles for accessibility */
.google-signin-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.3);
}

/* Responsive design */
@media (max-width: 640px) {
  .google-signin-btn {
    padding: 14px 16px;
    font-size: 16px;
  }

  .google-signin-btn__icon {
    font-size: 20px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .google-signin-btn--secondary {
    background-color: white;
    color: #e8eaed;
    border-color: #5f6368;
  }

  .google-signin-btn--secondary:hover:not(:disabled) {
    background-color: #3c4043;
    border-color: #8ab4f8;
  }

  .google-signin-btn--outline {
    background-color: transparent;
    color: #e8eaed;
    border-color: #5f6368;
  }

  .google-signin-btn--outline:hover:not(:disabled) {
    background-color: #3c4043;
    border-color: #8ab4f8;
  }
}
